package cn.iocoder.yudao.module.bpm.controller.admin.task;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.number.NumberUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmApprovalDetailReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmApprovalDetailRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceCreateReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskApproveReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskRejectReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import cn.iocoder.yudao.module.bpm.convert.task.BpmTaskConvert;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.yudao.module.bpm.service.definition.BpmFormService;
import cn.iocoder.yudao.module.bpm.service.definition.BpmProcessDefinitionService;
import cn.iocoder.yudao.module.bpm.service.task.BpmProcessInstanceService;
import cn.iocoder.yudao.module.bpm.service.task.BpmTaskService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.HistoricTaskInstanceEntityImpl;
import org.flowable.variable.service.impl.persistence.entity.HistoricVariableInstanceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import java.util.*;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSetByFlatMap;

@Tag(name = "内部服务调用 - 流程实例")
@RestController
@RequestMapping("/bpm/inner")
@Validated
@Slf4j
@PermitAll
public class BpmInnerController {

    @Resource
    private BpmProcessInstanceService processInstanceService;

    @Autowired
    private BpmTaskController bpmTaskController;

    @Resource
    private BpmTaskService taskService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private BpmFormService formService;

    @Resource
    private DeptApi deptApi;

    @Resource
    private RepositoryService repositoryService;

    @Resource
    private BpmProcessDefinitionService processDefinitionService;

    @PostMapping("/process-instance/create")
    public CommonResult<String> createProcessInstance(@Valid @RequestBody BpmProcessInstanceCreateReqVO createReqVO) {
        log.info("=BPM= /process-instance/create {}, {}", SecurityFrameworkUtils.getLoginUserId(), createReqVO);
        ModelQuery modelQuery = repositoryService.createModelQuery();
        modelQuery.modelKey(createReqVO.getProcessDefinitionId());
        Model model = modelQuery.list().get(0);
        ProcessDefinition processDefinition =
                processDefinitionService.getProcessDefinitionByDeploymentId(model.getDeploymentId());
        createReqVO.setProcessDefinitionId(processDefinition.getId());
        return success(processInstanceService.createProcessInstance(SecurityFrameworkUtils.getLoginUserId(), createReqVO));
    }

    @GetMapping("/process-instance/list-by-process-instance-id")
    public CommonResult<List<BpmTaskRespVO>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        List<HistoricTaskInstance> taskList = taskService.getTaskListByProcessInstanceId(processInstanceId, true);
        if (CollUtil.isEmpty(taskList)) {
            return success(Collections.emptyList());
        }

        // 拼接数据
        Set<Long> userIds = convertSetByFlatMap(taskList, task ->
                Stream.of(NumberUtils.parseLong(task.getAssignee()), NumberUtils.parseLong(task.getOwner())));
        Map<Long, AdminUserRespDTO> userMap = adminUserApi.getUserMap(userIds);
        Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(
                convertSet(userMap.values(), AdminUserRespDTO::getDeptId));
        // 获得 Form Map
        Map<Long, BpmFormDO> formMap = formService.getFormMap(
                convertSet(taskList, task -> NumberUtils.parseLong(task.getFormKey())));
        return success(BpmTaskConvert.INSTANCE.buildTaskListByProcessInstanceId(taskList,
                formMap, userMap, deptMap));
    }

    @PostMapping("/task/approve")
    public CommonResult<Boolean> approveTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        log.info("=BPM= /task/approve {}, {}", SecurityFrameworkUtils.getLoginUserId(), reqVO);
        String taskId = getTaskId(SecurityFrameworkUtils.getLoginUserId(), reqVO.getId());
        if (StringUtils.isNotBlank(taskId)) {
            reqVO.setId(taskId);
            taskService.approveTask(SecurityFrameworkUtils.getLoginUserId(), reqVO);
            return success(true);
        }
        return success(false);
    }

    private String getTaskId(Long userId, String processInstanceId) {
        List<HistoricTaskInstance> taskList = taskService.getTaskListByProcessInstanceId(processInstanceId, true);
        for (HistoricTaskInstance task  : taskList) {
            if (Objects.nonNull(userId) && userId.toString().equals(task.getAssignee())) {
                for (HistoricVariableInstanceEntity entity : ((HistoricTaskInstanceEntityImpl) task).getQueryVariables()) {
                    if (entity.getName().equals("TASK_STATUS")) {
                        Integer value = (Integer) entity.getValue();
                        if (value == 1) {
                            return task.getId();
                        }
                    }
                }
            }
        }
        return null;
    }

    @PostMapping("/task/reject")
    public CommonResult<Boolean> rejectTask(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {
        log.info("=BPM= /task/reject {}, {}", SecurityFrameworkUtils.getLoginUserId(), reqVO);
        String taskId = getTaskId(SecurityFrameworkUtils.getLoginUserId(), reqVO.getId());
        if (StringUtils.isNotBlank(taskId)) {
            reqVO.setId(taskId);
            taskService.rejectTask(SecurityFrameworkUtils.getLoginUserId(), reqVO);
            return success(true);
        }
        return success(false);
    }

    @PostMapping("/process-instance/get-approval-detail")
    public CommonResult<BpmApprovalDetailRespVO> getApprovalDetail(@Valid @RequestBody BpmApprovalDetailReqVO reqVO) {
        return success(processInstanceService.getApprovalDetail(SecurityFrameworkUtils.getLoginUserId(), reqVO));
    }
}
